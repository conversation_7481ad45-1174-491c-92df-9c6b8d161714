import 'dart:async';

import 'package:objectid/objectid.dart';
import 'package:talker_flutter/talker_flutter.dart';

import '../../objectbox.g.dart';
import '../models/erp_order.dart';
import '../providers/box_provider.dart';

class OrderRepository {
  final BoxProvider boxProvider;
  Talker get talker => boxProvider.talker;
  Box<ErpOrder> get box => Box<ErpOrder>(boxProvider.store);

  OrderRepository(this.boxProvider);

  // ==================== 基本 CRUD 操作 ====================

  /// 異步獲取所有訂單
  /// [includeDeleted] 是否包含已刪除的訂單
  Future<List<ErpOrder>> getAllAsync({bool includeDeleted = false}) async {
    try {
      if (includeDeleted) {
        // 返回所有訂單，包括已刪除的
        return await box.getAllAsync();
      } else {
        // 僅返回未刪除的訂單
        final query = box.query(ErpOrder_.deletedAt.isNull()).build();
        final results = await query.findAsync();
        query.close();
        return results;
      }
    } catch (e, s) {
      talker.error('Failed to get all orders async: $e', e, s);
      rethrow;
    }
  }

  /// 異步按ID獲取訂單
  Future<ErpOrder?> getByIdAsync(int id) async {
    try {
      return await box.getAsync(id);
    } catch (e, s) {
      talker.error('Failed to get order by id async: $e', e, s);
      rethrow;
    }
  }

  /// 異步按 ObjectId 獲取訂單
  Future<ErpOrder?> getByObjectIdAsync(String objectId) async {
    try {
      final query = box.query(ErpOrder_.objectId.equals(objectId)).build();
      final results = await query.findAsync();
      query.close();
      return results.isNotEmpty ? results.first : null;
    } catch (e, s) {
      talker.error('Failed to get order by objectId async: $e', e, s);
      rethrow;
    }
  }

  /// 異步保存訂單（新增或更新）
  Future<int> saveAsync(ErpOrder order) async {
    try {
      final future = order.id == null ? addAsync : updateAsync;
      return await future(order);
    } catch (e, s) {
      talker.error('Failed to save order async: $e', e, s);
      rethrow;
    }
  }

  /// 異步新增訂單
  Future<int> addAsync(ErpOrder order) async {
    try {
      // 設置創建時間和UUID（如果未提供）
      final now = DateTime.now();
      order.createdAt ??= now;
      order.updatedAt = now;
      order.objectId ??= ObjectId().hexString;

      return await box.putAsync(order);
    } catch (e, s) {
      talker.error('Failed to add order async: $e', e, s);
      rethrow;
    }
  }

  /// 異步更新訂單
  Future<int> updateAsync(ErpOrder order) async {
    try {
      // 確保更新時間已設置
      order.updatedAt = DateTime.now();

      return await box.putAsync(order);
    } catch (e, s) {
      talker.error('Failed to update order async: $e', e, s);
      rethrow;
    }
  }

  /// 異步批量添加或更新訂單
  Future<List<int>> putManyAsync(List<ErpOrder> orders) async {
    try {
      final now = DateTime.now();
      for (final order in orders) {
        // 如果是新物件，設置創建時間和UUID
        if (order.id == null) {
          order.createdAt ??= now;
          order.objectId ??= ObjectId().hexString;
        }
        order.updatedAt = now;
      }

      return await box.putManyAsync(orders);
    } catch (e, s) {
      talker.error('Failed to put many orders async: $e', e, s);
      rethrow;
    }
  }

  /// 異步軟刪除訂單（設置刪除時間而不是實際刪除）
  Future<bool> softDeleteAsync(int id) async {
    try {
      final order = await box.getAsync(id);
      if (order != null) {
        order.deletedAt = DateTime.now();
        await box.putAsync(order);
        return true;
      }
      return false;
    } catch (e, s) {
      talker.error('Failed to soft delete order async: $e', e, s);
      rethrow;
    }
  }

  /// 異步恢復已軟刪除的訂單
  Future<bool> restoreAsync(int id) async {
    try {
      final order = await box.getAsync(id);
      if (order != null && order.deletedAt != null) {
        order.deletedAt = null;
        await box.putAsync(order);
        return true;
      }
      return false;
    } catch (e, s) {
      talker.error('Failed to restore order async: $e', e, s);
      rethrow;
    }
  }

  /// 異步硬刪除訂單（真正從資料庫中刪除）
  Future<bool> hardDeleteAsync(int id) async {
    try {
      return await box.removeAsync(id);
    } catch (e, s) {
      talker.error('Failed to hard delete order async: $e', e, s);
      rethrow;
    }
  }

  /// 異步刪除所有訂單
  Future<int> deleteAllAsync() async {
    try {
      return await box.removeAllAsync();
    } catch (e, s) {
      talker.error('Failed to delete all orders async: $e', e, s);
      rethrow;
    }
  }

  // ==================== 查詢和搜索操作 ====================

  /// 異步按備注搜尋訂單
  Future<List<ErpOrder>> searchByNoteAsync(String note) async {
    try {
      final query = box
          .query(ErpOrder_.note.contains(note) &
              ErpOrder_.deletedAt.isNull())
          .build();
      final results = await query.findAsync();
      query.close();
      return results;
    } catch (e, s) {
      talker.error('Failed to search orders by note async: $e', e, s);
      rethrow;
    }
  }

  /// 異步按類型獲取訂單
  Future<List<ErpOrder>> getByTypeAsync(int type, {bool includeDeleted = false}) async {
    try {
      final condition = includeDeleted
          ? ErpOrder_.type.equals(type)
          : ErpOrder_.type.equals(type) & ErpOrder_.deletedAt.isNull();

      final query = box.query(condition).build();
      final results = await query.findAsync();
      query.close();
      return results;
    } catch (e, s) {
      talker.error('Failed to get orders by type async: $e', e, s);
      rethrow;
    }
  }

  /// 異步按分類獲取訂單
  Future<List<ErpOrder>> getByCategoryAsync(int categoryId, {bool includeDeleted = false}) async {
    try {
      final condition = includeDeleted
          ? ErpOrder_.parent.equals(categoryId)
          : ErpOrder_.parent.equals(categoryId) & ErpOrder_.deletedAt.isNull();

      final query = box.query(condition).build();
      final results = await query.findAsync();
      query.close();
      return results;
    } catch (e, s) {
      talker.error('Failed to get orders by category async: $e', e, s);
      rethrow;
    }
  }

  /// 異步按日期範圍獲取訂單
  Future<List<ErpOrder>> getByDateRangeAsync(
    DateTime startDate,
    DateTime endDate, {
    bool includeDeleted = false,
  }) async {
    try {
      final condition = includeDeleted
          ? ErpOrder_.createdAt.between(startDate.millisecondsSinceEpoch, endDate.millisecondsSinceEpoch)
          : ErpOrder_.createdAt.between(startDate.millisecondsSinceEpoch, endDate.millisecondsSinceEpoch) & ErpOrder_.deletedAt.isNull();

      final query = box.query(condition).build();
      final results = await query.findAsync();
      query.close();
      return results;
    } catch (e, s) {
      talker.error('Failed to get orders by date range async: $e', e, s);
      rethrow;
    }
  }

  /// 異步按金額範圍獲取訂單
  Future<List<ErpOrder>> getByAmountRangeAsync(
    double minAmount,
    double maxAmount, {
    bool includeDeleted = false,
  }) async {
    try {
      final condition = includeDeleted
          ? ErpOrder_.amount.between(minAmount, maxAmount)
          : ErpOrder_.amount.between(minAmount, maxAmount) & ErpOrder_.deletedAt.isNull();

      final query = box.query(condition).build();
      final results = await query.findAsync();
      query.close();
      return results;
    } catch (e, s) {
      talker.error('Failed to get orders by amount range async: $e', e, s);
      rethrow;
    }
  }

  /// 異步按觸發時間範圍獲取訂單
  Future<List<ErpOrder>> getByTriggerDateRangeAsync(
    DateTime startDate,
    DateTime endDate, {
    bool includeDeleted = false,
  }) async {
    try {
      final condition = includeDeleted
          ? ErpOrder_.triggerAt.between(startDate.millisecondsSinceEpoch, endDate.millisecondsSinceEpoch)
          : ErpOrder_.triggerAt.between(startDate.millisecondsSinceEpoch, endDate.millisecondsSinceEpoch) & ErpOrder_.deletedAt.isNull();

      final query = box.query(condition).build();
      final results = await query.findAsync();
      query.close();
      return results;
    } catch (e, s) {
      talker.error('Failed to get orders by trigger date range async: $e', e, s);
      rethrow;
    }
  }

  // ==================== 統計操作 ====================

  /// 異步獲取訂單總數
  Future<int> countAsync({bool includeDeleted = false}) async {
    try {
      if (includeDeleted) {
        // 獲取所有訂單並計算數量
        final orders = await box.getAllAsync();
        return orders.length;
      } else {
        // 使用查詢來計算未刪除的訂單數量
        final orders = await getAllAsync(includeDeleted: false);
        return orders.length;
      }
    } catch (e, s) {
      talker.error('Failed to count orders async: $e', e, s);
      rethrow;
    }
  }

  /// 異步獲取總金額
  Future<double> getTotalAmountAsync({bool includeDeleted = false}) async {
    try {
      final orders = await getAllAsync(includeDeleted: includeDeleted);
      return orders.fold<double>(0.0, (sum, order) => sum + (order.amount ?? 0.0));
    } catch (e, s) {
      talker.error('Failed to get total amount async: $e', e, s);
      rethrow;
    }
  }

  /// 異步按分類統計金額
  Future<Map<int, double>> getAmountByCategoryAsync({bool includeDeleted = false}) async {
    try {
      final orders = await getAllAsync(includeDeleted: includeDeleted);
      final Map<int, double> categoryAmounts = {};

      for (final order in orders) {
        final categoryId = order.parent.targetId;
        if (categoryId != 0) {
          categoryAmounts[categoryId] = (categoryAmounts[categoryId] ?? 0.0) + (order.amount ?? 0.0);
        }
      }

      return categoryAmounts;
    } catch (e, s) {
      talker.error('Failed to get amount by category async: $e', e, s);
      rethrow;
    }
  }

  /// 異步按類型統計金額
  Future<Map<int, double>> getAmountByTypeAsync({bool includeDeleted = false}) async {
    try {
      final orders = await getAllAsync(includeDeleted: includeDeleted);
      final Map<int, double> typeAmounts = {};

      for (final order in orders) {
        final type = order.type ?? 0;
        typeAmounts[type] = (typeAmounts[type] ?? 0.0) + (order.amount ?? 0.0);
      }

      return typeAmounts;
    } catch (e, s) {
      talker.error('Failed to get amount by type async: $e', e, s);
      rethrow;
    }
  }

  /// 異步按日期統計金額
  Future<Map<String, double>> getAmountByDateAsync({bool includeDeleted = false}) async {
    try {
      final orders = await getAllAsync(includeDeleted: includeDeleted);
      final Map<String, double> dateAmounts = {};

      for (final order in orders) {
        if (order.createdAt != null) {
          final dateKey = '${order.createdAt!.year}-${order.createdAt!.month.toString().padLeft(2, '0')}-${order.createdAt!.day.toString().padLeft(2, '0')}';
          dateAmounts[dateKey] = (dateAmounts[dateKey] ?? 0.0) + (order.amount ?? 0.0);
        }
      }

      return dateAmounts;
    } catch (e, s) {
      talker.error('Failed to get amount by date async: $e', e, s);
      rethrow;
    }
  }

  // ==================== 串流功能 ====================

  /// 監聽所有訂單變更的串流（包括已刪除的）
  /// 當資料庫中的訂單資料發生任何變更時，會自動發出最新的訂單列表
  Stream<List<ErpOrder>> watchAll() {
    try {
      return box
          .query()
          .watch(triggerImmediately: true)
          .map((query) => query.find())
          .handleError((error, stackTrace) {
        talker.error('Error in watchAll stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchAll stream: $e', e, s);
      rethrow;
    }
  }

  /// 監聽未刪除訂單變更的串流
  /// 當資料庫中的未刪除訂單資料發生變更時，會自動發出最新的訂單列表
  Stream<List<ErpOrder>> watchActive() {
    try {
      return box
          .query(ErpOrder_.deletedAt.isNull())
          .watch(triggerImmediately: true)
          .map((query) => query.find())
          .handleError((error, stackTrace) {
        talker.error('Error in watchActive stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchActive stream: $e', e, s);
      rethrow;
    }
  }

  /// 監聽特定分類訂單變更的串流
  /// 當指定分類的訂單資料發生變更時，會自動發出最新的訂單列表
  /// [categoryId] 要監聽的分類 ID
  Stream<List<ErpOrder>> watchByCategory(int categoryId) {
    try {
      return box
          .query(ErpOrder_.parent.equals(categoryId) &
              ErpOrder_.deletedAt.isNull())
          .watch(triggerImmediately: true)
          .map((query) => query.find())
          .handleError((error, stackTrace) {
        talker.error('Error in watchByCategory stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchByCategory stream: $e', e, s);
      rethrow;
    }
  }

  /// 監聽特定類型訂單變更的串流
  /// 當指定類型的訂單資料發生變更時，會自動發出最新的訂單列表
  /// [type] 要監聽的訂單類型
  Stream<List<ErpOrder>> watchByType(int type) {
    try {
      return box
          .query(ErpOrder_.type.equals(type) &
              ErpOrder_.deletedAt.isNull())
          .watch(triggerImmediately: true)
          .map((query) => query.find())
          .handleError((error, stackTrace) {
        talker.error('Error in watchByType stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchByType stream: $e', e, s);
      rethrow;
    }
  }

  /// 監聽特定備注搜尋結果的串流
  /// 當符合搜尋條件的訂單資料發生變更時，會自動發出最新的搜尋結果
  /// [note] 搜尋的備注內容（支援部分匹配）
  Stream<List<ErpOrder>> watchByNote(String note) {
    try {
      return box
          .query(ErpOrder_.note.contains(note) &
              ErpOrder_.deletedAt.isNull())
          .watch(triggerImmediately: true)
          .map((query) => query.find())
          .handleError((error, stackTrace) {
        talker.error('Error in watchByNote stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchByNote stream: $e', e, s);
      rethrow;
    }
  }

  /// 監聽訂單數量變更的串流
  /// 當訂單數量發生變更時，會自動發出最新的數量
  /// [includeDeleted] 是否包含已刪除的訂單
  Stream<int> watchCount({bool includeDeleted = false}) {
    try {
      return includeDeleted
          ? box
              .query()
              .watch(triggerImmediately: true)
              .map((query) => query.count())
          : box
              .query(ErpOrder_.deletedAt.isNull())
              .watch(triggerImmediately: true)
              .map((query) => query.count())
              .handleError((error, stackTrace) {
        talker.error('Error in watchCount stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchCount stream: $e', e, s);
      rethrow;
    }
  }

  /// 監聽總金額變更的串流
  /// 當訂單金額發生變更時，會自動發出最新的總金額
  /// [includeDeleted] 是否包含已刪除的訂單
  Stream<double> watchTotalAmount({bool includeDeleted = false}) {
    try {
      return includeDeleted
          ? box
              .query()
              .watch(triggerImmediately: true)
              .map((query) => query.find().fold<double>(0.0, (sum, order) => sum + (order.amount ?? 0.0)))
          : box
              .query(ErpOrder_.deletedAt.isNull())
              .watch(triggerImmediately: true)
              .map((query) => query.find().fold<double>(0.0, (sum, order) => sum + (order.amount ?? 0.0)))
              .handleError((error, stackTrace) {
        talker.error('Error in watchTotalAmount stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchTotalAmount stream: $e', e, s);
      rethrow;
    }
  }

  /// 監聽特定 ID 訂單變更的串流
  /// 當指定 ID 的訂單資料發生變更時，會自動發出最新的訂單資料
  /// [id] 要監聽的訂單 ID
  Stream<ErpOrder?> watchById(int id) {
    try {
      return box
          .query(ErpOrder_.id.equals(id))
          .watch(triggerImmediately: true)
          .map((query) {
        final results = query.find();
        return results.isNotEmpty ? results.first : null;
      }).handleError((error, stackTrace) {
        talker.error('Error in watchById stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchById stream: $e', e, s);
      rethrow;
    }
  }

  /// 監聽特定 ObjectId 訂單變更的串流
  /// 當指定 ObjectId 的訂單資料發生變更時，會自動發出最新的訂單資料
  /// [objectId] 要監聽的訂單 ObjectId
  Stream<ErpOrder?> watchByObjectId(String objectId) {
    try {
      return box
          .query(ErpOrder_.objectId.equals(objectId))
          .watch(triggerImmediately: true)
          .map((query) {
        final results = query.find();
        return results.isNotEmpty ? results.first : null;
      }).handleError((error, stackTrace) {
        talker.error('Error in watchByObjectId stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchByObjectId stream: $e', e, s);
      rethrow;
    }
  }

  /// 監聽已刪除訂單的串流
  /// 當已刪除的訂單資料發生變更時，會自動發出最新的已刪除訂單列表
  Stream<List<ErpOrder>> watchDeleted() {
    try {
      return box
          .query(ErpOrder_.deletedAt.notNull())
          .watch(triggerImmediately: true)
          .map((query) => query.find())
          .handleError((error, stackTrace) {
        talker.error('Error in watchDeleted stream: $error', error, stackTrace);
      });
    } catch (e, s) {
      talker.error('Failed to create watchDeleted stream: $e', e, s);
      rethrow;
    }
  }
}